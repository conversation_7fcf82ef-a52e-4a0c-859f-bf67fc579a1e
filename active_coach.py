#!/usr/bin/env python3
"""
Intelligent AI Learning Coach - Context-Aware Version
Implements truly intelligent coaching using vector database as brain for decision making.
Follows comprehensive documentation requirements for personalized, proactive coaching.
"""

import os
import sqlite3
import json
import datetime
import time
import threading
import chromadb
import google.generativeai as genai
from dotenv import load_dotenv
import warnings

# Suppress deprecation warnings for cleaner interface
warnings.filterwarnings("ignore", category=DeprecationWarning)

# --- CONFIGURATION AND CONSTANTS ---
load_dotenv()

DB_PATH = "db/user_memory.db"
DATA_DIR = "data"
CHROMA_DB_PATH = "db/knowledge_vectordb"
KNOWLEDGE_BASE_COLLECTION_NAME = "ics_knowledge_base"
MASTER_CURRICULUM_PATH = os.path.join(
    DATA_DIR, "curriculum", "master_curriculum.json")
DEFAULT_USER_ID = 1  # Make this configurable later


class IntelligentCoachingBrain:
    """Intelligent coaching brain that uses vector database for decision making."""

    def __init__(self, db_path, collection, user_id=DEFAULT_USER_ID):
        self.db_path = db_path
        self.collection = collection
        self.user_id = user_id
        self.conversation_context = []  # Keep for immediate context
        self.user_patterns = {}
        self.session_start = datetime.datetime.now()
        self.session_id = f"session_{int(self.session_start.timestamp())}"

    def extract_user_information(self, user_input):
        """Automatically extract and store user information from conversations."""
        user_input_lower = user_input.lower()
        extracted_info = {}

        # Extract academic background
        if any(word in user_input_lower for word in ["computational linguistics", "nlp", "programming", "python"]):
            extracted_info["academic_field"] = "computational_linguistics"
        elif any(word in user_input_lower for word in ["phonetics", "linguistics", "spanish", "ipa"]):
            extracted_info["academic_field"] = "phonetics_linguistics"

        # Extract study patterns
        if any(word in user_input_lower for word in ["distracted", "chess", "procrastinate"]):
            extracted_info["distraction_pattern"] = user_input

        # Extract time preferences
        if any(word in user_input_lower for word in ["morning", "afternoon", "evening", "night"]):
            extracted_info["time_preference"] = user_input

        # Extract struggles
        if any(word in user_input_lower for word in ["struggle", "difficult", "hard", "problem"]):
            extracted_info["learning_challenge"] = user_input

        return extracted_info

    def analyze_conversation_patterns(self):
        """Analyze conversation patterns to identify user needs."""
        if len(self.conversation_context) < 3:
            return None

        recent_messages = self.conversation_context[-5:]

        # Look for recurring themes
        themes = {}
        for msg in recent_messages:
            if "distract" in msg.lower():
                themes["distraction"] = themes.get("distraction", 0) + 1
            if "procrastinat" in msg.lower():
                themes["procrastination"] = themes.get(
                    "procrastination", 0) + 1
            if "schedule" in msg.lower():
                themes["scheduling"] = themes.get("scheduling", 0) + 1

        # Return dominant theme if it appears multiple times
        for theme, count in themes.items():
            if count >= 2:
                return theme

        return None

    def save_conversation_to_memory(self, message_content, message_type, extracted_insights=None):
        """Save conversation to persistent memory database."""
        conn = sqlite3.connect(self.db_path)
        cursor = conn.cursor()

        try:
            cursor.execute("""
                INSERT INTO Conversation_Memory
                (user_id, session_id, message_type, message_content, extracted_insights)
                VALUES (?, ?, ?, ?, ?)
            """, (self.user_id, self.session_id, message_type, message_content,
                  json.dumps(extracted_insights) if extracted_insights else None))
            conn.commit()
        except Exception as e:
            print(f"Error saving conversation: {e}")
        finally:
            conn.close()

    def get_relevant_conversation_history(self, current_topic, limit=10):
        """Get relevant conversation history using semantic search."""
        try:
            # First try to find relevant conversations from database
            conn = sqlite3.connect(self.db_path)
            cursor = conn.cursor()

            # Get recent conversations from this session
            cursor.execute("""
                SELECT message_type, message_content, timestamp
                FROM Conversation_Memory
                WHERE user_id = ? AND session_id = ?
                ORDER BY timestamp DESC LIMIT ?
            """, (self.user_id, self.session_id, limit))

            recent_conversations = cursor.fetchall()
            conn.close()

            if recent_conversations:
                # Format for context
                formatted_history = []
                for msg_type, content, timestamp in reversed(recent_conversations):
                    role = "👤 You" if msg_type == "user_input" else "🤖 Coach"
                    formatted_history.append(f"{role}: {content}")
                return formatted_history
            else:
                # Fallback to in-memory context
                return self.conversation_context[-6:] if self.conversation_context else []

        except Exception as e:
            print(f"Error retrieving conversation history: {e}")
            return self.conversation_context[-6:] if self.conversation_context else []

    def analyze_conversation_patterns_from_db(self):
        """Analyze conversation patterns from database for learning and improvement."""
        conn = sqlite3.connect(self.db_path)
        cursor = conn.cursor()

        try:
            # Get recent conversations for pattern analysis
            cursor.execute("""
                SELECT message_content, extracted_insights, timestamp
                FROM Conversation_Memory
                WHERE user_id = ? AND message_type = 'user_input'
                ORDER BY timestamp DESC LIMIT 50
            """, (self.user_id,))

            recent_messages = cursor.fetchall()

            patterns = {
                "common_topics": {},
                "recurring_struggles": [],
                "learning_preferences": {},
                "time_patterns": {}
            }

            for content, insights_json, timestamp in recent_messages:
                # Analyze common topics
                if "spacing" in content.lower():
                    patterns["common_topics"]["spacing"] = patterns["common_topics"].get(
                        "spacing", 0) + 1
                if "traffic light" in content.lower():
                    patterns["common_topics"]["traffic_light"] = patterns["common_topics"].get(
                        "traffic_light", 0) + 1

                # Analyze extracted insights
                if insights_json:
                    try:
                        insights = json.loads(insights_json)
                        if "distraction_pattern" in insights:
                            patterns["recurring_struggles"].append(
                                insights["distraction_pattern"])
                    except:
                        pass

            return patterns

        except Exception as e:
            print(f"Error analyzing conversation patterns: {e}")
            return {}
        finally:
            conn.close()

    def check_foundation_readiness(self, requested_topic):
        """Check if user has proper foundations before allowing advanced topics - DYNAMIC VERSION."""
        # Get current module from database
        conn = sqlite3.connect(self.db_path)
        cursor = conn.cursor()

        try:
            cursor.execute("""
                SELECT current_module FROM Course_Progress
                WHERE user_id = ? ORDER BY last_updated DESC LIMIT 1
            """, (self.user_id,))
            result = cursor.fetchone()
            current_module = result[0] if result else 'module_00'
        except:
            current_module = 'module_00'
        finally:
            conn.close()

        # DYNAMIC: Query the master curriculum to find which module contains this topic
        required_module = self._find_topic_in_curriculum(requested_topic)

        if required_module and required_module > current_module:
            return {
                'ready': False,
                'required_module': required_module,
                'current_module': current_module,
                'guidance': f"That's covered in {required_module}. Let's focus on your current module first."
            }

        return {'ready': True}

    def _find_topic_in_curriculum(self, topic):
        """Dynamically find which module contains a specific topic by querying master curriculum."""
        try:
            # Query the master curriculum for this topic
            curriculum_results = self.collection.query(
                query_texts=[f"{topic} module curriculum"],
                n_results=3,
                where={"type": "master_curriculum"}
            )

            if curriculum_results['documents'] and curriculum_results['documents'][0]:
                # Parse the curriculum content to find module
                content = curriculum_results['documents'][0]

                # Look for module indicators in the content
                if 'module_04' in content.lower() or 'module 4' in content.lower():
                    return 'module_04'
                elif 'module_03' in content.lower() or 'module 3' in content.lower():
                    return 'module_03'
                elif 'module_02' in content.lower() or 'module 2' in content.lower():
                    return 'module_02'
                elif 'module_01' in content.lower() or 'module 1' in content.lower():
                    return 'module_01'
                elif 'module_00' in content.lower() or 'module 0' in content.lower():
                    return 'module_00'

            # If not found in curriculum, assume it's advanced (safe default)
            return 'module_04'

        except Exception as e:
            print(f"Error finding topic in curriculum: {e}")
            # Safe default - assume advanced topic
            return 'module_04'

    def query_our_knowledge_first(self, user_input):
        """Query OUR knowledge base first before falling back to external knowledge."""
        try:
            # Query our master curriculum first
            curriculum_results = self.collection.query(
                query_texts=[user_input],
                n_results=5,
                where={"type": "master_curriculum"}
            )

            if curriculum_results['documents'] and curriculum_results['documents'][0]:
                return {
                    "source": "master_curriculum",
                    "content": curriculum_results['documents'][0],
                    "confidence": "high"
                }

            # Query our other knowledge data
            knowledge_results = self.collection.query(
                query_texts=[user_input],
                n_results=5,
                where={"type": "knowledge"}
            )

            if knowledge_results['documents'] and knowledge_results['documents'][0]:
                return {
                    "source": "our_knowledge_base",
                    "content": knowledge_results['documents'][0],
                    "confidence": "medium"
                }

            # No results from our data
            return None

        except Exception as e:
            print(f"Error querying our knowledge: {e}")
            return None

    def query_knowledge_for_guidance(self, query_text, n_results=5):
        """Query the vector database for intelligent guidance."""
        try:
            results = self.collection.query(
                query_texts=[query_text],
                n_results=n_results
            )

            if results['documents'] and results['documents'][0]:
                return results['documents'][0]
            return []
        except Exception as e:
            print(f"Knowledge query error: {e}")
            return []

    def make_intelligent_decision(self, user_input, context):
        """Use vector database as brain to make intelligent coaching decisions."""
        # FIRST: Check if user is ready for requested topic (COACHING GUIDANCE)
        foundation_check = self.check_foundation_readiness(user_input)

        # SECOND: Query OUR knowledge base for relevant information
        our_knowledge = self.query_our_knowledge_first(user_input)

        # Analyze current situation
        pattern = self.analyze_conversation_patterns()
        extracted_info = self.extract_user_information(user_input)

        # Build decision context with coaching guidance prioritized
        decision_context = {
            "user_input": user_input,
            "foundation_check": foundation_check,  # Coaching guidance comes first
            "conversation_pattern": pattern,
            "extracted_info": extracted_info,
            "our_knowledge": our_knowledge,
            "knowledge_source": "our_data" if our_knowledge else "external",
            "session_duration": (datetime.datetime.now() - self.session_start).total_seconds() / 60,
            "external_context": context
        }

        return decision_context

    def check_proactive_triggers(self):
        """Check for intelligent proactive coaching triggers with anti-repetition."""
        now = datetime.datetime.now()
        triggers = []

        # Check if we've already sent a message recently to avoid repetition
        if hasattr(self, 'last_proactive_message_time'):
            time_since_last = (
                now - self.last_proactive_message_time).total_seconds()
            if time_since_last < 300:  # Don't send proactive messages within 5 minutes
                return triggers

        # Intelligent time-based triggers (not rigid)
        if 23 <= now.hour or now.hour <= 5:
            triggers.append({
                "type": "sleep_guidance",
                "message": f"It's {now.strftime('%I:%M %p')} - quite late for studying! Your brain consolidates memories during sleep. Consider wrapping up for better learning tomorrow.",
                "priority": "high",
                "natural": True
            })

        # Check for learning patterns that need intervention
        conn = sqlite3.connect(self.db_path)
        cursor = conn.cursor()

        # Check for practice gaps (intelligent, not algorithmic)
        cursor.execute("""
            SELECT MAX(log_date) FROM Practice_Log WHERE user_id = ?
        """, (self.user_id,))
        last_practice = cursor.fetchone()[0]
        if last_practice:
            last_practice_date = datetime.datetime.strptime(
                last_practice, '%Y-%m-%d %H:%M:%S')
            days_since_practice = (now - last_practice_date).days
            if days_since_practice >= 2:
                triggers.append({
                    "type": "practice_gap",
                    "message": f"I notice it's been {days_since_practice} days since your last practice session. What's been happening? Sometimes life gets in the way - let's figure out how to get back on track.",
                    "priority": "medium",
                    "natural": True
                })

        conn.close()
        return triggers

    def generate_agentic_opening(self):
        """Generate truly agentic opening that immediately addresses what needs to be done."""
        now = datetime.datetime.now()

        # Get comprehensive user context for intelligent analysis
        conn = sqlite3.connect(self.db_path)
        cursor = conn.cursor()

        # Check critical foundations
        cursor.execute("""
            SELECT COUNT(*) FROM Scheduled_Events
            WHERE user_id = ? AND event_type = 'university'
        """, (self.user_id,))
        has_schedule = cursor.fetchone()[0] > 0

        # Check user's start date and expected progress
        cursor.execute("""
            SELECT name, start_date, current_main_focus FROM User_Profile WHERE user_id = ?
        """, (self.user_id,))
        profile_result = cursor.fetchone()
        if profile_result:
            user_name = profile_result[0] or "there"
            start_date_str = profile_result[1]
            current_focus = profile_result[2] or "Enablers"
            try:
                start_date = datetime.datetime.strptime(
                    start_date_str, '%Y-%m-%d')
                days_since_start = (now - start_date).days
            except:
                days_since_start = 0
        else:
            user_name = "there"
            current_focus = "Enablers"
            days_since_start = 0

        # Check recent practice activity
        cursor.execute("""
            SELECT COUNT(*) FROM Practice_Log
            WHERE user_id = ? AND log_date >= date('now', '-7 days')
        """, (self.user_id,))
        recent_practice_count = cursor.fetchone()[0]

        # Check skills progress
        cursor.execute("""
            SELECT COUNT(*) FROM Skills
            WHERE user_id = ? AND competence_level IN ('Developing', 'Stabilized', 'Mastered')
        """, (self.user_id,))
        skills_developed = cursor.fetchone()[0]

        conn.close()

        # Generate AGENTIC opening that immediately addresses issues - DYNAMIC USER NAME
        time_greeting = f"Good morning {user_name}! Early start at {now.strftime('%I:%M %p')}"
        if now.hour >= 23 or now.hour <= 5:
            time_greeting = f"{user_name}, it's {now.strftime('%I:%M %p')} - quite late!"
        elif 12 <= now.hour <= 18:
            time_greeting = f"Afternoon {user_name}!"
        elif 18 <= now.hour <= 23:
            time_greeting = f"Evening {user_name}!"

        # CRITICAL ISSUE DETECTION - Address immediately
        if not has_schedule:
            return f"{time_greeting} I need to address something critical: you don't have a schedule system set up yet. This is blocking ALL your progress - we can't build effective learning habits without it. Should we fix this RIGHT NOW? (It'll take 10 minutes)"

        # COURSE PROGRESSION ANALYSIS
        if days_since_start > 7 and skills_developed == 0:
            return f"{time_greeting} I'm concerned - you've been working on this for {days_since_start} days but haven't developed any tracked skills yet. We need to accelerate your Module 0: Rapid Start completion. Can we focus on that today?"

        if recent_practice_count == 0:
            return f"{time_greeting} I notice you haven't practiced anything this week. Let's get you back on track with a quick 15-minute session. What's been blocking you from practicing?"

        # NORMAL AGENTIC GUIDANCE
        if 4 <= now.hour <= 6:
            return f"{time_greeting} Perfect time for deep work! Since you're in {current_focus} phase, I suggest we tackle your most challenging concept first while your mind is fresh. Ready to dive in?"
        else:
            return f"{time_greeting} Based on your {current_focus} progress ({recent_practice_count} sessions this week), I think we should focus on advancing to the next level. Shall we assess where you are and plan the next steps?"


class ContextualAssessmentEngine:
    """Manages comprehensive user assessment and context awareness."""

    def __init__(self, db_path, collection, user_id=DEFAULT_USER_ID):
        self.db_path = db_path
        self.collection = collection
        self.user_id = user_id

    def perform_comprehensive_assessment(self, user_id=None):
        """Perform comprehensive user assessment as described in documentation."""
        if user_id is None:
            user_id = self.user_id

        conn = sqlite3.connect(self.db_path)
        cursor = conn.cursor()

        # Check for critical foundations
        cursor.execute("""
            SELECT COUNT(*) FROM Scheduled_Events
            WHERE user_id = ? AND event_type = 'university'
        """, (user_id,))
        has_schedule = cursor.fetchone()[0] > 0

        # Check skill levels
        cursor.execute("""
            SELECT concept_id, competence_level, confidence_score
            FROM Skills WHERE user_id = ?
        """, (user_id,))
        skills = cursor.fetchall()

        conn.close()

        # Generate assessment
        assessment = {
            "critical_missing": [],
            "strengths": [],
            "weaknesses": [],
            "recommendations": []
        }

        if not has_schedule:
            assessment["critical_missing"].append("schedule_system")

        # Analyze skills
        enabler_skills = [s for s in skills if "enabler" in s[0]]
        if len(enabler_skills) == 0:
            assessment["critical_missing"].append("enabler_foundation")

        return assessment

    def get_user_context(self, user_id=None):
        """Get comprehensive user context for intelligent coaching."""
        if user_id is None:
            user_id = self.user_id

        conn = sqlite3.connect(self.db_path)
        cursor = conn.cursor()

        # Get user profile
        cursor.execute(
            "SELECT * FROM User_Profile WHERE user_id = ?", (user_id,))
        profile = cursor.fetchone()

        # Get recent practice sessions
        cursor.execute("""
            SELECT * FROM Practice_Log
            WHERE user_id = ?
            ORDER BY log_date DESC LIMIT 5
        """, (user_id,))
        recent_practice = cursor.fetchall()

        # Get current skills
        cursor.execute("""
            SELECT concept_id, competence_level, confidence_score
            FROM Skills WHERE user_id = ?
        """, (user_id,))
        skills = cursor.fetchall()

        conn.close()

        return {
            "profile": profile,
            "recent_practice": recent_practice,
            "skills": skills,
            "assessment": self.perform_comprehensive_assessment(user_id)
        }


class IntelligentRoadmapEngine:
    """Generates dynamic roadmaps based on curriculum and user progress."""

    def __init__(self, db_path, collection, user_id=DEFAULT_USER_ID):
        self.db_path = db_path
        self.collection = collection
        self.user_id = user_id

    def load_all_knowledge_data(self):
        """Load all knowledge from vector database for intelligent decisions."""
        try:
            # Query for all available techniques and concepts
            results = self.collection.query(
                query_texts=["learning techniques methods strategies"],
                n_results=50  # Get more comprehensive knowledge
            )
            return results['documents'][0] if results['documents'] else []
        except Exception as e:
            print(f"Error loading knowledge: {e}")
            return []

    def generate_personalized_roadmap(self, user_context):
        """Generate personalized roadmap based on user assessment and curriculum."""
        assessment = user_context["assessment"]

        roadmap = {
            "immediate_priorities": [],
            "next_steps": [],
            "long_term_goals": []
        }

        # Critical missing foundations get highest priority
        if "schedule_system" in assessment["critical_missing"]:
            roadmap["immediate_priorities"].append({
                "priority": "CRITICAL",
                "action": "Set up comprehensive schedule system",
                "reason": "Foundation missing - blocks all other progress"
            })

        if "enabler_foundation" in assessment["critical_missing"]:
            # Get user's actual current module dynamically
            current_module_info = self._get_user_current_module_info()
            current_module = current_module_info.get(
                'current_module', 'module_00')

            # Query vector database for current module's next lesson
            curriculum_query = self.query_curriculum_for_next_lesson(
                f"{current_module_info.get('module_name', 'Module 0')} next lesson curriculum structure")

            roadmap["immediate_priorities"].append({
                "priority": "HIGH",
                "action": f"Continue with {current_module_info.get('module_name', 'Module 0')} - next lesson based on current progress",
                "reason": f"User is currently in {current_module_info.get('module_name', 'Module 0')} ({current_module_info.get('completion_percentage', 0)}% complete)",
                "current_module": current_module,
                "curriculum_data": curriculum_query
            })

        # Use vector database to determine actual next lesson
        if len(roadmap["immediate_priorities"]) == 0:
            next_lesson = self.query_curriculum_for_next_lesson(
                "current user progress next lesson")
            roadmap["next_steps"].append({
                "action": "Continue with structured curriculum progression",
                "next_lesson": next_lesson
            })

        return roadmap

    def _get_user_current_module_info(self):
        """Get user's current module info for dynamic roadmap generation."""
        conn = sqlite3.connect(self.db_path)
        cursor = conn.cursor()

        try:
            cursor.execute("""
                SELECT current_module, module_status, completion_percentage, notes
                FROM Course_Progress WHERE user_id = ?
                ORDER BY last_updated DESC LIMIT 1
            """, (self.user_id,))
            result = cursor.fetchone()

            if result:
                current_module, status, completion, notes = result
                module_name = self._get_module_name_from_curriculum(
                    current_module)

                return {
                    'current_module': current_module,
                    'module_name': module_name,
                    'status': status,
                    'completion_percentage': completion or 0.0,
                    'notes': notes or "No notes"
                }
            else:
                # Default to Module 0
                return {
                    'current_module': 'module_00',
                    'module_name': 'Module 0: Rapid Start',
                    'status': 'not_started',
                    'completion_percentage': 0.0,
                    'notes': "Starting curriculum"
                }
        except sqlite3.OperationalError:
            # Default to Module 0 if table doesn't exist
            return {
                'current_module': 'module_00',
                'module_name': 'Module 0: Rapid Start',
                'status': 'not_started',
                'completion_percentage': 0.0,
                'notes': "Starting curriculum"
            }
        finally:
            conn.close()

    def _get_module_name_from_curriculum(self, module_id):
        """Get module name dynamically from master curriculum."""
        try:
            import json
            with open(MASTER_CURRICULUM_PATH, 'r') as f:
                curriculum = json.load(f)

            for module in curriculum.get('modules', []):
                if module.get('module_id') == module_id:
                    return module.get('module_name', module_id)

            return module_id
        except Exception as e:
            print(f"Error loading master curriculum: {e}")
            return module_id

    def query_curriculum_for_next_lesson(self, query_context):
        """Query vector database for curriculum structure and next lesson."""
        try:
            # Query specifically for master curriculum
            curriculum_results = self.collection.query(
                query_texts=[
                    f"curriculum modules units topics {query_context}"],
                n_results=5,
                # Prioritize master curriculum
                where={"type": "master_curriculum"}
            )

            # If no master curriculum results, fall back to general knowledge
            if not curriculum_results['documents'] or not curriculum_results['documents'][0]:
                curriculum_results = self.collection.query(
                    query_texts=[
                        f"curriculum modules lessons order {query_context}"],
                    n_results=10
                )

            if curriculum_results['documents'] and curriculum_results['documents'][0]:
                return curriculum_results['documents'][0]
            return []
        except Exception as e:
            print(f"Error querying curriculum: {e}")
            return []


class IntelligentAICoach:
    """Intelligent AI Coach that uses vector database as brain for decision making."""

    def __init__(self):
        self.setup_ai()
        self.setup_database()
        self.setup_knowledge_base()

        # Initialize intelligent components
        self.coaching_brain = IntelligentCoachingBrain(
            DB_PATH, self.collection, DEFAULT_USER_ID)
        self.assessment_engine = ContextualAssessmentEngine(
            DB_PATH, self.collection, DEFAULT_USER_ID)
        self.roadmap_engine = IntelligentRoadmapEngine(
            DB_PATH, self.collection, DEFAULT_USER_ID)

        self.is_running = False
        self.proactive_thread = None
        self.user_id = DEFAULT_USER_ID

    def setup_ai(self):
        """Configure the AI model."""
        api_key = os.getenv('GOOGLE_API_KEY')
        if not api_key:
            raise ValueError("GOOGLE_API_KEY not found in .env file")
        genai.configure(api_key=api_key)
        self.model = genai.GenerativeModel('gemini-2.0-flash-exp')

    def setup_database(self):
        """Setup database connection and comprehensive tables."""
        os.makedirs("db", exist_ok=True)
        conn = sqlite3.connect(DB_PATH)
        cursor = conn.cursor()

        # Create comprehensive database schema as per documentation
        cursor.execute('''
            CREATE TABLE IF NOT EXISTS User_Profile (
                user_id INTEGER PRIMARY KEY,
                name TEXT NOT NULL,
                start_date DATE DEFAULT CURRENT_DATE,
                archetype TEXT DEFAULT 'Unchained',
                current_main_focus TEXT DEFAULT 'Enablers',
                timezone TEXT DEFAULT 'UTC',
                preferred_session_length INTEGER DEFAULT 25,
                daily_study_goal_minutes INTEGER DEFAULT 120,
                created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
                last_active DATETIME DEFAULT CURRENT_TIMESTAMP
            )
        ''')

        cursor.execute('''
            CREATE TABLE IF NOT EXISTS Skills (
                skill_id INTEGER PRIMARY KEY AUTOINCREMENT,
                user_id INTEGER NOT NULL,
                concept_id TEXT NOT NULL,
                competence_level TEXT DEFAULT 'Unknown',
                last_practiced_date DATE,
                practice_count INTEGER DEFAULT 0,
                is_priority BOOLEAN DEFAULT 0,
                confidence_score REAL DEFAULT 0.0,
                last_updated DATETIME DEFAULT CURRENT_TIMESTAMP,
                FOREIGN KEY (user_id) REFERENCES User_Profile (user_id)
            )
        ''')

        cursor.execute('''
            CREATE TABLE IF NOT EXISTS Practice_Log (
                log_id INTEGER PRIMARY KEY AUTOINCREMENT,
                user_id INTEGER NOT NULL,
                skill_id INTEGER,
                log_date DATETIME DEFAULT CURRENT_TIMESTAMP,
                duration_minutes INTEGER NOT NULL,
                user_notes_difficulty TEXT,
                confidence_score INTEGER CHECK (confidence_score >= 1 AND confidence_score <= 5),
                coach_intervention_id TEXT,
                session_type TEXT DEFAULT 'practice',
                effectiveness_rating INTEGER CHECK (effectiveness_rating >= 1 AND effectiveness_rating <= 5),
                FOREIGN KEY (user_id) REFERENCES User_Profile (user_id),
                FOREIGN KEY (skill_id) REFERENCES Skills (skill_id)
            )
        ''')

        cursor.execute('''
            CREATE TABLE IF NOT EXISTS Scheduled_Events (
                event_id INTEGER PRIMARY KEY AUTOINCREMENT,
                user_id INTEGER NOT NULL,
                event_description TEXT NOT NULL,
                event_type TEXT NOT NULL,
                scheduled_start_time DATETIME NOT NULL,
                scheduled_end_time DATETIME NOT NULL,
                is_completed BOOLEAN DEFAULT 0,
                is_recurring BOOLEAN DEFAULT 0,
                recurrence_pattern TEXT,
                related_skill_id INTEGER,
                related_goal_id INTEGER,
                priority_level INTEGER DEFAULT 3,
                location TEXT,
                preparation_time_minutes INTEGER DEFAULT 0,
                created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
                FOREIGN KEY (user_id) REFERENCES User_Profile (user_id),
                FOREIGN KEY (related_skill_id) REFERENCES Skills (skill_id)
            )
        ''')

        cursor.execute('''
            CREATE TABLE IF NOT EXISTS Coach_Feedback (
                feedback_id INTEGER PRIMARY KEY AUTOINCREMENT,
                user_id INTEGER NOT NULL,
                coach_response_length INTEGER,
                user_feedback_type TEXT, -- 'too_long', 'too_short', 'just_right', 'confusing', 'helpful'
                context_topic TEXT,
                user_comment TEXT,
                timestamp DATETIME DEFAULT CURRENT_TIMESTAMP,
                FOREIGN KEY (user_id) REFERENCES User_Profile (user_id)
            )
        ''')

        cursor.execute('''
            CREATE TABLE IF NOT EXISTS Conversation_Memory (
                memory_id INTEGER PRIMARY KEY AUTOINCREMENT,
                user_id INTEGER NOT NULL,
                session_id TEXT NOT NULL,
                message_type TEXT NOT NULL, -- 'user_input', 'coach_response'
                message_content TEXT NOT NULL,
                extracted_insights TEXT, -- JSON of extracted patterns/info
                timestamp DATETIME DEFAULT CURRENT_TIMESTAMP,
                FOREIGN KEY (user_id) REFERENCES User_Profile (user_id)
            )
        ''')

        # Create default user if none exists
        cursor.execute("SELECT COUNT(*) FROM User_Profile")
        if cursor.fetchone()[0] == 0:
            # Set start date to 10 days ago to test progression logic
            start_date = (datetime.datetime.now() -
                          datetime.timedelta(days=10)).strftime('%Y-%m-%d')
            cursor.execute("""
                INSERT INTO User_Profile (user_id, name, start_date)
                VALUES (?, 'User', ?)
            """, (DEFAULT_USER_ID, start_date,))

        conn.commit()
        conn.close()

    def get_user_name(self):
        """Get user's name dynamically from database."""
        conn = sqlite3.connect(DB_PATH)
        cursor = conn.cursor()
        try:
            cursor.execute(
                "SELECT name FROM User_Profile WHERE user_id = ?", (self.user_id,))
            result = cursor.fetchone()
            return result[0] if result else "there"
        except Exception:
            return "there"
        finally:
            conn.close()

    def generate_dynamic_response_templates(self, current_module_info):
        """Generate dynamic response templates based on current context."""
        current_module = current_module_info.get('current_module', 'module_00')
        module_name = current_module_info.get('module_name', 'Module 0')

        return {
            'advanced_topic_redirect': f"That's a great question about [topic] - it's covered in a later module. But first, let's make sure your {module_name} foundations are solid...",
            'missing_foundations': f"Before we dive into that, I need to check - do you have the essential foundations for {module_name}? That's critical for everything else to work.",
            'module_guidance': f"Since you're in {module_name}, let's focus on what you need to master here first."
        }

    def setup_knowledge_base(self):
        """Setup ChromaDB knowledge base and load data."""
        os.makedirs(CHROMA_DB_PATH, exist_ok=True)
        self.chroma_client = chromadb.PersistentClient(path=CHROMA_DB_PATH)
        self.collection = self.chroma_client.get_or_create_collection(
            name=KNOWLEDGE_BASE_COLLECTION_NAME)

        # Load knowledge data if collection is empty
        if self.collection.count() == 0:
            self.load_knowledge_from_data_directory()

    def load_knowledge_from_data_directory(self):
        """Load knowledge from data directory into vector database."""
        data_files = []

        # Scan data directory for JSON files
        for root, _, files in os.walk(DATA_DIR):
            for file in files:
                if file.endswith('.json'):
                    data_files.append(os.path.join(root, file))

        documents = []
        metadatas = []
        ids = []

        # Load JSON files
        for file_path in data_files:
            try:
                with open(file_path, 'r', encoding='utf-8') as f:
                    data = json.load(f)

                if isinstance(data, list):
                    for i, item in enumerate(data):
                        if isinstance(item, dict):
                            doc_text = json.dumps(item)
                            documents.append(doc_text)
                            metadatas.append(
                                {"source": file_path, "type": "knowledge"})
                            ids.append(f"{file_path}_{i}")

            except Exception as e:
                print(f"Error loading {file_path}: {e}")

        # Load the master curriculum as the single source of truth
        master_curriculum_path = os.path.join(
            DATA_DIR, "curriculum", "master_curriculum.json")
        if os.path.exists(master_curriculum_path):
            try:
                with open(master_curriculum_path, 'r', encoding='utf-8') as f:
                    curriculum_content = f.read()
                    documents.append(curriculum_content)
                    metadatas.append(
                        {"source": master_curriculum_path, "type": "master_curriculum"})
                    ids.append("master_curriculum")
                    print("✅ Loaded master curriculum as single source of truth")
            except Exception as e:
                print(f"Error loading master curriculum: {e}")

        if documents:
            self.collection.add(
                documents=documents,
                metadatas=metadatas,
                ids=ids
            )
            print(
                f"Loaded {len(documents)} knowledge documents from {len(data_files)} files + syllabus")

    def start_proactive_monitoring(self):
        """Start intelligent proactive coaching monitoring."""
        self.is_running = True
        self.proactive_thread = threading.Thread(
            target=self._proactive_monitoring_loop)
        self.proactive_thread.daemon = True
        self.proactive_thread.start()
        print("🤖 Intelligent coaching monitoring started...")

    def stop_proactive_monitoring(self):
        """Stop the proactive coaching monitoring."""
        self.is_running = False
        if self.proactive_thread:
            self.proactive_thread.join()

    def _proactive_monitoring_loop(self):
        """Intelligent background monitoring loop."""
        # Wait a bit before starting proactive monitoring to avoid conflicts with opening
        time.sleep(10)

        while self.is_running:
            try:
                triggers = self.coaching_brain.check_proactive_triggers()
                for trigger in triggers:
                    if trigger["priority"] == "high" and trigger.get("natural"):
                        # Only send if user hasn't been active recently
                        if not hasattr(self, 'last_user_interaction') or \
                           (datetime.datetime.now() - self.last_user_interaction).total_seconds() > 600:
                            self._send_natural_proactive_message(trigger)

                time.sleep(600)  # Check every 10 minutes (less frequent)
            except Exception as e:
                print(f"Error in proactive monitoring: {e}")
                time.sleep(60)

    def _send_natural_proactive_message(self, trigger):
        """Send natural, non-rigid proactive coaching message."""
        print(f"\n🤖 Coach: {trigger['message']}")

        # Mark the time to prevent repetition
        self.coaching_brain.last_proactive_message_time = datetime.datetime.now()

        # Store intervention in database for pattern analysis
        conn = sqlite3.connect(DB_PATH)
        cursor = conn.cursor()
        cursor.execute("""
            INSERT OR IGNORE INTO Practice_Log (user_id, duration_minutes, user_notes_difficulty, session_type)
            VALUES (?, ?, ?, ?)
        """, (DEFAULT_USER_ID, 0, f"Proactive intervention: {trigger['type']}", "coaching"))
        conn.commit()
        conn.close()

    def respond(self, user_input):
        """Generate intelligent, context-aware coaching response."""
        # Track user interaction time
        self.last_user_interaction = datetime.datetime.now()

        # Add to conversation context (for immediate use)
        self.coaching_brain.conversation_context.append(user_input)

        # Save to persistent memory database
        extracted_info = self.coaching_brain.extract_user_information(
            user_input)
        self.coaching_brain.save_conversation_to_memory(
            user_input, "user_input", extracted_info)

        # Store extracted information in user profile
        if extracted_info:
            self._store_extracted_information(extracted_info)

        # Get comprehensive user context
        user_context = self.assessment_engine.get_user_context()

        # Make intelligent decision using vector database as brain
        decision_context = self.coaching_brain.make_intelligent_decision(
            user_input, user_context)

        # Generate personalized roadmap if needed
        roadmap = self.roadmap_engine.generate_personalized_roadmap(
            user_context)

        # Build intelligent coaching prompt
        prompt = self._build_intelligent_prompt(
            user_input, user_context, decision_context, roadmap)

        # Generate response using AI
        try:
            response = self.model.generate_content(prompt)
            response_text = response.text

            # Add response to conversation context (for immediate use)
            self.coaching_brain.conversation_context.append(response_text)

            # Save response to persistent memory database
            self.coaching_brain.save_conversation_to_memory(
                response_text, "coach_response")

            return response_text
        except Exception as e:
            return f"I'm having trouble processing that right now. Could you rephrase? (Error: {e})"

    def _get_user_current_module(self):
        """Dynamically get user's current module status from database."""
        conn = sqlite3.connect(DB_PATH)
        cursor = conn.cursor()

        # Check if Course_Progress table exists and get current module
        try:
            cursor.execute("""
                SELECT current_module, module_status, completion_percentage, notes
                FROM Course_Progress WHERE user_id = ?
                ORDER BY last_updated DESC LIMIT 1
            """, (DEFAULT_USER_ID,))
            progress_result = cursor.fetchone()

            if progress_result:
                current_module, status, completion, notes = progress_result

                # Get module name dynamically from master curriculum
                module_name = self._get_module_name_from_curriculum(
                    current_module)

                return {
                    'current_module': current_module,
                    'module_name': module_name,
                    'status': status,
                    'completion_percentage': completion,
                    'notes': notes or "No notes"
                }
            else:
                # Default to Module 0 if no progress found
                return {
                    'current_module': 'module_00',
                    'module_name': 'Module 0: Rapid Start',
                    'status': 'not_started',
                    'completion_percentage': 0.0,
                    'notes': "Starting curriculum"
                }

        except sqlite3.OperationalError:
            # Table doesn't exist, create it and default to Module 0
            cursor.execute('''
                CREATE TABLE IF NOT EXISTS Course_Progress (
                    progress_id INTEGER PRIMARY KEY AUTOINCREMENT,
                    user_id INTEGER NOT NULL,
                    current_module TEXT DEFAULT 'module_00',
                    module_status TEXT DEFAULT 'in_progress',
                    completion_percentage REAL DEFAULT 0.0,
                    last_updated DATETIME DEFAULT CURRENT_TIMESTAMP,
                    notes TEXT,
                    FOREIGN KEY (user_id) REFERENCES User_Profile (user_id)
                )
            ''')

            cursor.execute("""
                INSERT INTO Course_Progress (user_id, current_module)
                VALUES (?, 'module_00')
            """, (DEFAULT_USER_ID,))
            conn.commit()

            return {
                'current_module': 'module_00',
                'module_name': 'Module 0: Rapid Start',
                'status': 'in_progress',
                'completion_percentage': 0.0,
                'notes': "Just started curriculum"
            }
        finally:
            conn.close()

    def _get_module_name_from_curriculum(self, module_id):
        """Get module name dynamically from master curriculum."""
        try:
            import json
            with open(MASTER_CURRICULUM_PATH, 'r') as f:
                curriculum = json.load(f)

            for module in curriculum.get('modules', []):
                if module.get('module_id') == module_id:
                    return module.get('module_name', module_id)

            # Fallback if not found
            return module_id
        except Exception as e:
            print(f"Error loading master curriculum: {e}")
            return module_id

    def update_user_module_progress(self, module_id, completion_percentage, notes=None):
        """Update user's module progress dynamically."""
        conn = sqlite3.connect(DB_PATH)
        cursor = conn.cursor()

        try:
            # Update or insert progress
            cursor.execute("""
                INSERT OR REPLACE INTO Course_Progress
                (user_id, current_module, module_status, completion_percentage, notes, last_updated)
                VALUES (?, ?, ?, ?, ?, CURRENT_TIMESTAMP)
            """, (DEFAULT_USER_ID, module_id, 'in_progress' if completion_percentage < 100 else 'completed',
                  completion_percentage, notes))

            conn.commit()
            print(
                f"📈 Updated progress: {module_id} - {completion_percentage}% complete")

        except Exception as e:
            print(f"Error updating progress: {e}")
        finally:
            conn.close()

    def _store_extracted_information(self, extracted_info):
        """Store automatically extracted user information in database."""
        conn = sqlite3.connect(DB_PATH)
        cursor = conn.cursor()

        # Store academic field if detected
        if "academic_field" in extracted_info:
            cursor.execute("""
                UPDATE User_Profile SET archetype = ? WHERE user_id = ?
            """, (extracted_info["academic_field"], DEFAULT_USER_ID))

        # Store distraction patterns
        if "distraction_pattern" in extracted_info:
            cursor.execute("""
                INSERT OR IGNORE INTO Practice_Log (user_id, duration_minutes, user_notes_difficulty, session_type)
                VALUES (?, ?, ?, ?)
            """, (DEFAULT_USER_ID, 0, extracted_info["distraction_pattern"], "pattern_detection"))

        conn.commit()
        conn.close()

    def _build_intelligent_prompt(self, user_input, user_context, decision_context, roadmap):
        """Build intelligent coaching prompt using full context and knowledge base."""
        now = datetime.datetime.now()

        # Get user profile info dynamically
        user_name = self.get_user_name()

        # Get assessment info
        assessment = user_context.get("assessment", {})
        critical_missing = assessment.get("critical_missing", [])

        # Get coaching guidance and knowledge from decision context
        foundation_check = decision_context.get("foundation_check", {})
        our_knowledge = decision_context.get("our_knowledge")

        # Build foundation guidance
        if not foundation_check.get('ready', True):
            foundation_guidance = f"⚠️ COACHING ALERT: User asked about advanced topic but isn't ready. {foundation_check.get('guidance', '')}"
        else:
            foundation_guidance = "✅ User is ready for this topic based on current module progress."

        if our_knowledge:
            knowledge_summary = f"RELEVANT iCanStudy INFORMATION:\n{our_knowledge['content']}"
        else:
            knowledge_summary = "No specific iCanStudy information found for this topic - use general knowledge but mark it clearly"

        # Get conversation history from persistent memory
        conversation_history = self.coaching_brain.get_relevant_conversation_history(
            user_input, limit=6)
        conversation_history_text = "\n".join(
            conversation_history) if conversation_history else "No previous conversation"

        # DYNAMIC MODULE DETECTION - Get user's actual current module from database
        current_module_info = self._get_user_current_module()

        # Generate dynamic response templates based on current context
        response_templates = self.generate_dynamic_response_templates(
            current_module_info)

        # Build intelligent, context-aware prompt
        prompt = f"""You are an AGENTIC AI Learning Coach specialized in the iCanStudy methodology. You MUST be dynamic and intelligent, not hardcoded.

CRITICAL CONTEXT AWARENESS:
Time: {now.strftime('%A, %B %d, %Y at %I:%M %p')}
User ({user_name}) says: "{user_input}"

CONVERSATION HISTORY (REMEMBER THIS):
{conversation_history_text}

USER'S CURRENT MODULE STATUS (DYNAMIC):
{current_module_info}

USER ASSESSMENT & ROADMAP:
Critical Missing: {critical_missing}
Recent Practice: {len(user_context.get('recent_practice', []))} sessions in recent history
Current Skills: {len(user_context.get('skills', []))} tracked skills

IMMEDIATE PRIORITIES (FOLLOW THESE):
{roadmap.get('immediate_priorities', [])}

NEXT STEPS IN ROADMAP:
{roadmap.get('next_steps', [])}

COACHING FOUNDATION CHECK:
{foundation_guidance}

RELEVANT iCanStudy MATERIALS:
{knowledge_summary}

CRITICAL KNOWLEDGE RULES:
1. ALWAYS use information from the iCanStudy materials first (shown above)
2. If no information found in iCanStudy materials, you may use general knowledge but MUST mark it as: "This isn't from the iCanStudy system, but generally speaking..."
3. Never make up technique names or concepts not in the iCanStudy materials
4. Follow iCanStudy terminology exactly: "spacing" not "spaced repetition", "retrieval" not "recall"
5. Sound natural and conversational, not robotic

CRITICAL COACHING GUIDELINES (FOLLOW THESE STRICTLY):
1. **FOUNDATIONS FIRST**: Always assess Enabler skills (time, focus, procrastination) before suggesting advanced techniques
2. **3-PILLAR ORDER**: Enablers → Retrieval → Encoding. Never recommend advanced techniques if foundations are missing
3. **GUIDE BACK TO CURRENT MODULE**: If user asks about advanced topics, use this template: "{response_templates['advanced_topic_redirect']}"
4. **PROACTIVE COACHING**: Don't just answer questions - actively guide toward next steps in current module
5. **ASSESS BEFORE ADVANCING**: If user struggles with higher-order skills, first check if foundational system is failing
6. **CURRICULUM PROGRESSION**: Use the roadmap to keep user on track, not just answer random questions
7. **ANSWER THEN REDIRECT**: Briefly answer the question, then guide back to current module work
8. **PRIORITIZE OUR KNOWLEDGE**: Always check our knowledge base first, mark external knowledge clearly

COACHING BEHAVIOR (DYNAMIC TEMPLATES):
- Advanced topics: "{response_templates['advanced_topic_redirect']}"
- Missing foundations: "{response_templates['missing_foundations']}"
- Module guidance: "{response_templates['module_guidance']}"
- Always connect answers back to current module progress and user's actual situation

RESPONSE STYLE:
- NORMAL: 2-3 sentences + question for confirmation
- EXPLAINING CONCEPTS: Up to 5 sentences if needed, then check understanding
- CROSS-MODULE QUESTIONS: Explain the technique + where it fits in curriculum + current module guidance
- ALWAYS stay relevant to the conversation and user's actual progress

Your intelligent, DYNAMIC response that adapts to the user's actual module status:"""

        return prompt


def main():
    """Main application loop with intelligent, agentic coaching."""
    print("🎯 Intelligent AI Learning Coach - Agentic Version")
    print("💬 I'm your intelligent learning partner with comprehensive knowledge base")
    print("🧠 Specialized for Computational Linguistics & Phonetics/Linguistics")
    print("⚡ Uses vector database as brain for intelligent coaching decisions")
    print("🚪 Type 'quit' to exit")
    print("\n🔄 Initializing intelligent coaching brain...")

    try:
        # Show loading progress
        print("📚 Loading knowledge base...")
        coach = IntelligentAICoach()

        print("🧠 Analyzing your context...")
        # Start proactive monitoring
        coach.start_proactive_monitoring()

        print("✅ Ready! Generating intelligent opening...\n")

        # Generate intelligent, agentic opening message
        agentic_opening = coach.coaching_brain.generate_agentic_opening()
        print(f"🤖 Coach: {agentic_opening}")

        # CRITICAL: Store the opening message in conversation context
        coach.coaching_brain.conversation_context.append(agentic_opening)

        # Mark that we've sent an opening message to prevent immediate proactive triggers
        coach.coaching_brain.last_proactive_message_time = datetime.datetime.now()

        while True:
            try:
                user_input = input("\n👤 You: ").strip()

                if user_input.lower() in ['quit', 'exit', 'bye']:
                    user_name = coach.get_user_name()
                    print(
                        f"🤖 Coach: Great session, {user_name}! I'll remember our conversation and continue learning about your patterns. Keep building those learning habits!")
                    break

                if not user_input:
                    # Even empty input gets an intelligent response
                    user_name = coach.get_user_name()
                    print(
                        f"🤖 Coach: I'm here when you're ready, {user_name}. Take your time to think about what you'd like to work on, or just let me know how you're feeling about your learning journey right now.")
                    continue

                # Generate intelligent coaching response
                response = coach.respond(user_input)
                print(f"\n🤖 Coach: {response}")

            except KeyboardInterrupt:
                print(
                    "\n🤖 Coach: Session interrupted. I'll remember where we left off. Keep up the great work!")
                break
            except Exception as e:
                print(f"Error: {e}")

    except Exception as e:
        print(f"Failed to initialize coach: {e}")
    finally:
        try:
            coach.stop_proactive_monitoring()
        except:
            pass


if __name__ == "__main__":
    main()
